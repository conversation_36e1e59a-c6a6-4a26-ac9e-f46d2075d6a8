import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { FlowProcessStep } from './flow-process-step.entity';

type FindOneOptions = {
  id?: EntityId;
};

@Injectable()
export class FlowProcessStepService {
  constructor(
    @InjectRepository(FlowProcessStep)
    private readonly flowProcessStepRepository: Repository<FlowProcessStep>,
  ) {}

  private normalizeEntityValues(entity: Partial<FlowProcessStep>): Partial<FlowProcessStep> {
    const flowProcessStep = new FlowProcessStep();
    Object.assign(flowProcessStep, entity);
    if (flowProcessStep.description === '') flowProcessStep.description = null;
    return flowProcessStep;
  }

  async findOneBy(options: FindOneOptions): Promise<FlowProcessStep | null> {
    return await this.flowProcessStepRepository.findOneBy(options);
  }

  async findAll({ flowProcessId }: { flowProcessId: EntityId }): Promise<FlowProcessStep[]> {
    return await this.flowProcessStepRepository.find({
      where: {
        flowProcess: { id: flowProcessId },
      },
    });
  }

  async findMaxFlowProcessStepIndex(): Promise<number> {
    const result = await this.flowProcessStepRepository
      .createQueryBuilder('entity')
      .select('MAX(entity.index)')
      .getRawOne<{ max: number }>();

    return result?.max ?? 0;
  }

  async createFlowProcessStep(data: {
    flowProcessStep: Pick<FlowProcessStep, 'name' | 'description' | 'index' | 'parentId'>;
    flowProcessId: EntityId;
  }): Promise<FlowProcessStep> {
    const flowProcessStep = this.flowProcessStepRepository.create({
      ...this.normalizeEntityValues(data.flowProcessStep),
      flowProcess: { id: data.flowProcessId },
    });
    return await this.flowProcessStepRepository.save(flowProcessStep);
  }

  async updateFlowProcessStep(
    flowProcessStep: FlowProcessStep,
    data: Pick<FlowProcessStep, 'name' | 'description'>,
  ): Promise<FlowProcessStep> {
    const result = await this.flowProcessStepRepository.update(
      flowProcessStep.id,
      this.normalizeEntityValues(data),
    );
    return result.affected ? Object.assign(flowProcessStep, data) : flowProcessStep;
  }

  async deleteFlowProcessStep(flowProcessStep: FlowProcessStep): Promise<FlowProcessStep> {
    await this.flowProcessStepRepository.delete(flowProcessStep.id);
    return flowProcessStep;
  }
}
