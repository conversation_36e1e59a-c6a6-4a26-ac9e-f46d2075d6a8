import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Flow } from './flow.entity';

type FindOneOptions = {
  id?: EntityId;
};

@Injectable()
export class FlowService {
  constructor(@InjectRepository(Flow) private readonly flowRepository: Repository<Flow>) {}

  private normalizeEntityValues(entity: Partial<Flow>): Partial<Flow> {
    const flow = new Flow();
    Object.assign(flow, entity);
    if (flow.description === '') flow.description = null;
    return flow;
  }

  async findOneBy(options: FindOneOptions): Promise<Flow | null> {
    return await this.flowRepository.findOneBy(options);
  }

  async findAll(): Promise<Flow[]> {
    return await this.flowRepository.find();
  }

  async findMaxFlowIndex(): Promise<number> {
    const result = await this.flowRepository
      .createQueryBuilder('entity')
      .select('MAX(entity.index)')
      .getRawOne<{ max: number }>();

    return result?.max ?? 0;
  }

  async createFlow(data: Pick<Flow, 'name' | 'description' | 'index'>): Promise<Flow> {
    const flow = this.flowRepository.create(this.normalizeEntityValues(data));
    return await this.flowRepository.save(flow);
  }

  async updateFlow(flow: Flow, data: Pick<Flow, 'name' | 'description'>): Promise<Flow> {
    const result = await this.flowRepository.update(flow.id, this.normalizeEntityValues(data));
    return result.affected ? Object.assign(flow, data) : flow;
  }

  async deleteFlow(flow: Flow): Promise<Flow> {
    await this.flowRepository.delete(flow.id);
    return flow;
  }
}
