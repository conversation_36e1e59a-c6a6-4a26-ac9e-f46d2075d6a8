import { Injectable, NotFoundException } from '@nestjs/common';
import { FlowProcessStep, FlowProcessStepService } from '@core/database';
import { UpdateFlowProcessStepDto } from '../dto';

@Injectable()
export class UpdateFlowProcessStepUseCase implements UseCase {
  constructor(private readonly flowProcessStepService: FlowProcessStepService) {}

  async execute(id: EntityId, dto: UpdateFlowProcessStepDto): Promise<FlowProcessStep> {
    const { name, description } = dto;

    const flowProcessStep = await this.flowProcessStepService.findOneBy({ id });
    if (!flowProcessStep)
      throw new NotFoundException('Flow process step not found', 'FLOW_PROCESS_STEP_NOT_FOUND');

    return await this.flowProcessStepService.updateFlowProcessStep(flowProcessStep, { name, description });
  }
}
