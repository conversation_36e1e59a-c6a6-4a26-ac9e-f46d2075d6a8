import { Modu<PERSON> } from '@nestjs/common';
import { APP_FILTER, APP_PIPE } from '@nestjs/core';
import { ZodValidationPipe } from 'nestjs-zod';
import { GlobalApiExceptionFilter } from '@libs/common/api';
import { EnvsModule, includeAppEnvFilePaths } from '@libs/common/envs';
import { EnvVariablesSchema } from './config';
import { FlowsModule } from './features/flows/flows.module';

@Module({
  imports: [
    EnvsModule.forRoot({
      schema: EnvVariablesSchema,
      envFilePath: includeAppEnvFilePaths('core'),
    }),

    FlowsModule,
  ],
  providers: [
    {
      provide: APP_PIPE,
      useClass: ZodValidationPipe,
    },
    {
      provide: APP_FILTER,
      useClass: GlobalApiExceptionFilter,
    },
  ],
})
export class CoreModule {}
