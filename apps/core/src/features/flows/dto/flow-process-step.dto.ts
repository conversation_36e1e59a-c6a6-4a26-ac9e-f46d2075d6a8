import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

const FlowProcessStepSchema = z.object({
  id: z.string().describe('Flow process step id'),
  name: z.string().describe('Flow process step name'),
  description: z.string().nullable().describe('Flow process step description'),
  index: z.number().describe('Flow process step index'),
  parentId: z.string().nullable().describe('Flow process step parent id'),
});

export class FlowProcessStepDto extends createZodDto(FlowProcessStepSchema) {}

const CreateFlowProcessStepSchema = FlowProcessStepSchema.pick({
  name: true,
  description: true,
  parentId: true,
});

export class CreateFlowProcessStepDto extends createZodDto(CreateFlowProcessStepSchema) {}

const UpdateFlowProcessStepSchema = FlowProcessStepSchema.pick({
  name: true,
  description: true,
});

export class UpdateFlowProcessStepDto extends createZodDto(UpdateFlowProcessStepSchema) {}
