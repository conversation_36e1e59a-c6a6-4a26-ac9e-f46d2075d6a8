import { Injectable } from '@nestjs/common';
import { FlowProcessStep, FlowProcessStepService } from '@core/database';
import { CreateFlowProcessStepDto } from '../dto';

@Injectable()
export class CreateFlowProcessStepUseCase implements UseCase {
  constructor(private readonly flowProcessStepService: FlowProcessStepService) {}

  async execute(flowProcessId: EntityId, dto: CreateFlowProcessStepDto): Promise<FlowProcessStep> {
    const nextIndex = await this.flowProcessStepService.findMaxFlowProcessStepIndex();

    return await this.flowProcessStepService.createFlowProcessStep({
      flowProcessStep: { ...dto, index: nextIndex + 1 },
      flowProcessId,
    });
  }
}
