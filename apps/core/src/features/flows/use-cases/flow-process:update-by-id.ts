import { Injectable, NotFoundException } from '@nestjs/common';
import { FlowProcess, FlowProcessService } from '@core/database';
import { UpdateFlowProcessDto } from '../dto';

@Injectable()
export class UpdateFlowProcessUseCase implements UseCase {
  constructor(private readonly flowProcessService: FlowProcessService) {}

  async execute(id: EntityId, dto: UpdateFlowProcessDto): Promise<FlowProcess> {
    const { name, description } = dto;

    const flowProcess = await this.flowProcessService.findOneBy({ id });
    if (!flowProcess) throw new NotFoundException('Flow process not found', 'FLOW_PROCESS_NOT_FOUND');

    return await this.flowProcessService.updateFlowProcess(flowProcess, { name, description });
  }
}
