import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { FlowProcess } from './flow-process.entity';

@Entity('flow-process-steps')
export class FlowProcessStep {
  @PrimaryGeneratedColumn()
  id: EntityId;

  // @Column({ type: 'varchar' })
  // type: string;

  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string | null;

  @Column({ type: 'smallint' })
  index: number;

  @Column({ type: 'integer', nullable: true })
  parentId: EntityId | null;

  @ManyToOne(() => FlowProcess, process => process.steps, {
    nullable: false,
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'flowProcessId', referencedColumnName: 'id' })
  flowProcess: FlowProcess;
}
