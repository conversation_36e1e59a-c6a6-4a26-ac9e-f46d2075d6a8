import { Injectable, NotFoundException } from '@nestjs/common';
import { FlowProcessStep, FlowProcessStepService } from '@core/database';

@Injectable()
export class DeleteFlowProcessStepByIdUseCase implements UseCase {
  constructor(private readonly flowProcessStepService: FlowProcessStepService) {}

  async execute(id: EntityId): Promise<FlowProcessStep> {
    const flowProcessStep = await this.flowProcessStepService.findOneBy({ id });

    if (!flowProcessStep)
      throw new NotFoundException('Flow process step not found', 'FLOW_PROCESS_STEP_NOT_FOUND');

    return await this.flowProcessStepService.deleteFlowProcessStep(flowProcessStep);
  }
}
