import { Injectable, NotFoundException } from '@nestjs/common';
import { Flow, FlowService } from '@core/database';
import { UpdateFlowDto } from '../dto';

@Injectable()
export class UpdateFlowUseCase implements UseCase {
  constructor(private readonly flowService: FlowService) {}

  async execute(id: EntityId, dto: UpdateFlowDto): Promise<Flow> {
    const { name, description } = dto;

    const flow = await this.flowService.findOneBy({ id });
    if (!flow) throw new NotFoundException('Flow not found', 'FLOW_NOT_FOUND');

    return await this.flowService.updateFlow(flow, { name, description });
  }
}
