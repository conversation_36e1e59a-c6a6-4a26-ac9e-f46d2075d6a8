import { Injectable } from '@nestjs/common';
import { FlowProcess, FlowProcessService } from '@core/database';

@Injectable()
export class GetAllFlowProcessesUseCase implements UseCase {
  constructor(private readonly flowProcessService: FlowProcessService) {}

  async execute({ flowId }: { flowId: EntityId }): Promise<FlowProcess[]> {
    return await this.flowProcessService.findAll({ flowId });
  }
}
