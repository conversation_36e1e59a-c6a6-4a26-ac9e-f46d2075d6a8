import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { patchNestJsSwagger } from 'nestjs-zod';
import { CoreModule } from './core.module';

patchNestJsSwagger();

async function bootstrap() {
  const app = await NestFactory.create(CoreModule);

  app.setGlobalPrefix('/api');

  /** swagger setup */
  const openApiConfig = new DocumentBuilder()
    .setTitle('API')
    .setVersion('0.0.0')
    // .addBearerAuth({ type: 'http', scheme: 'bearer' }, ACCESS_TOKEN_SECURITY_KEY)
    .build();

  const openApi = SwaggerModule.createDocument(app, openApiConfig);
  /** webpage: /swagger */
  SwaggerModule.setup('swagger', app, openApi, {
    jsonDocumentUrl: 'swagger/json',
  });

  const config = app.get(ConfigService);

  await app.listen(config.get('PORT') ?? 3000);
}

void bootstrap();
