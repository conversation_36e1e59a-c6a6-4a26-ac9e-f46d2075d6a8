import { Injectable, NotFoundException } from '@nestjs/common';
import { FlowProcess, FlowProcessService } from '@core/database';

@Injectable()
export class GetFlowProcessByIdUseCase implements UseCase {
  constructor(private readonly flowProcessService: FlowProcessService) {}

  async execute(id: EntityId): Promise<FlowProcess> {
    const flowProcess = await this.flowProcessService.findOneBy({ id });

    if (!flowProcess) throw new NotFoundException('Flow process not found', 'FLOW_PROCESS_NOT_FOUND');

    return flowProcess;
  }
}
