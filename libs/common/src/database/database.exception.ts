export enum DbExceptionCode {
  NotFound,
  Conflict,
  BadData,
  UnexpectedError,
}

export class DbException extends Error {
  // to indicate this exception may be shared in the response or not
  internal: boolean;
  code: DbExceptionCode;

  constructor(params: { message: string; code: DbExceptionCode; internal?: boolean }) {
    const { message, code, internal = true } = params;
    super();
    this.name = 'DbException';
    this.message = message;
    this.code = code;
    this.internal = internal;
  }
}
