import { Injectable, NotFoundException } from '@nestjs/common';
import { Flow, FlowService } from '@core/database';

@Injectable()
export class DeleteFlowByIdUseCase implements UseCase {
  constructor(private readonly flowService: FlowService) {}

  // TODO: update indexes
  // TODO: delete children?

  async execute(id: EntityId): Promise<Flow> {
    const flow = await this.flowService.findOneBy({ id });

    if (!flow) throw new NotFoundException('Flow not found', 'FLOW_NOT_FOUND');

    return await this.flowService.deleteFlow(flow);
  }
}
