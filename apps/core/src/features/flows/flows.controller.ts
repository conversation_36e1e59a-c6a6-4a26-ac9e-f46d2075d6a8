import { Body, Controller, Delete, Get, HttpStatus, Param, Post, Put } from '@nestjs/common';
import { ApiParam } from '@nestjs/swagger';
import { ApiSuccessfulResponse, HttpResponse } from '@libs/common/api';
import { CreateFlowDto, FlowDto, UpdateFlowDto } from './dto/flow.dto';
import {
  CreateFlowUseCase,
  DeleteFlowByIdUseCase,
  GetAllFlowsUseCase,
  GetFlowByIdUseCase,
  UpdateFlowUseCase,
} from './use-cases';

@Controller('flows')
export class FlowsController {
  constructor(
    private readonly createFlowUseCase: CreateFlowUseCase,
    private readonly updateFlowUseCase: UpdateFlowUseCase,
    private readonly deleteFlowByIdUseCase: DeleteFlowByIdUseCase,
    private readonly getFlowByIdUseCase: GetFlowByIdUseCase,
    private readonly getAllFlowsUseCase: GetAllFlowsUseCase,
  ) {}

  @Post()
  @ApiSuccessfulResponse(HttpStatus.CREATED, 'Flow created', FlowDto)
  async createFlow(@Body() dto: CreateFlowDto) {
    const flow = await this.createFlowUseCase.execute(dto);
    return new HttpResponse({ data: flow, message: 'Flow created' });
  }

  @Delete(':id')
  @ApiParam({ name: 'id', required: true, description: 'Flow identifier', type: 'string' })
  @ApiSuccessfulResponse(HttpStatus.OK, 'Flow deleted', FlowDto)
  async deleteFlow(@Param('id') id: EntityId) {
    const flow = await this.deleteFlowByIdUseCase.execute(id);
    return new HttpResponse({ data: flow, message: 'Flow deleted' });
  }

  @Get(':id')
  @ApiParam({ name: 'id', required: true, description: 'Flow identifier', type: 'string' })
  @ApiSuccessfulResponse(HttpStatus.OK, 'Flow found', FlowDto)
  async getFlow(@Param('id') id: EntityId) {
    const flow = await this.getFlowByIdUseCase.execute(id);
    return new HttpResponse({ data: flow, message: 'Flow found' });
  }

  @Put(':id')
  @ApiParam({ name: 'id', required: true, description: 'Flow identifier', type: 'string' })
  @ApiSuccessfulResponse(HttpStatus.OK, 'Flow updated', FlowDto)
  async updateFlow(@Param('id') id: EntityId, @Body() dto: UpdateFlowDto) {
    const flow = await this.updateFlowUseCase.execute(id, dto);
    return new HttpResponse({ data: flow, message: 'Flow updated' });
  }

  @Get()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Flows found', [FlowDto])
  async getAllFlows() {
    const flows = await this.getAllFlowsUseCase.execute();
    return new HttpResponse({ data: flows, message: 'Flows found' });
  }
}
