import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { isDevelopment } from '@libs/common/envs';
import { EnvironmentVariables } from '../config';
import {
  Flow,
  FlowService,
  FlowProcess,
  FlowProcessService,
  FlowProcessStep,
  FlowProcessStepService,
} from './flow';

@Module({
  imports: [
    TypeOrmModule.forRootAsync({
      inject: [ConfigService],
      useFactory: (config: ConfigService<EnvironmentVariables>) => ({
        type: 'postgres',
        host: config.get('PG_HOST'),
        port: config.get('PG_PORT'),
        password: config.get('PG_PASSWORD'),
        username: config.get('PG_USER'),
        database: config.get('PG_DB'),
        // entities: [Flow, FlowProcess, FlowProcessStep],
        autoLoadEntities: true,
        synchronize: true,
        logging: isDevelopment(),
      }),
    }),

    TypeOrmModule.forFeature([Flow, FlowProcess, FlowProcessStep]),
  ],

  providers: [FlowService, FlowProcessService, FlowProcessStepService],
  exports: [FlowService, FlowProcessService, FlowProcessStepService],
})
export class DatabaseModule {}
