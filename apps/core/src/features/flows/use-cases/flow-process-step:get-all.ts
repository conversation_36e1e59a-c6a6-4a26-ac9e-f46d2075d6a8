import { Injectable } from '@nestjs/common';
import { FlowProcessStep, FlowProcessStepService } from '@core/database';

@Injectable()
export class GetAllFlowProcessStepsUseCase implements UseCase {
  constructor(private readonly flowProcessStepService: FlowProcessStepService) {}

  async execute({ flowProcessId }: { flowProcessId: EntityId }): Promise<FlowProcessStep[]> {
    return await this.flowProcessStepService.findAll({ flowProcessId });
  }
}
