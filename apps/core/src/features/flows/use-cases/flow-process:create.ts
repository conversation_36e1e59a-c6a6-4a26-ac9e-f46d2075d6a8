import { Injectable } from '@nestjs/common';
import { FlowProcess, FlowProcessService } from '@core/database';
import { CreateFlowProcessDto } from '../dto';

@Injectable()
export class CreateFlowProcessUseCase implements UseCase {
  constructor(private readonly flowProcessService: FlowProcessService) {}

  async execute(flowId: EntityId, dto: CreateFlowProcessDto): Promise<FlowProcess> {
    const nextIndex = await this.flowProcessService.findMaxFlowProcessIndex();

    return await this.flowProcessService.createFlowProcess({
      flowProcess: { ...dto, index: nextIndex + 1 },
      flowId,
    });
  }
}
